import 'dart:developer';
import 'package:bloc/bloc.dart';
import 'package:dio/dio.dart';
import 'get_report_state.dart';
import '../../models/api_model/report_data_model.dart';

class GetReportDataCubit extends Cubit<GetReportState> {
  final Dio dio;
  final String baseUrl = "https://8ec4-197-35-43-30.ngrok-free.app/api/";

  GetReportDataCubit({required this.dio}) : super(GetReportInitial());

  Future<void> getReport(String token) async {
    emit(GetReportLoading());
    try {
      final response = await dio.get(
        '${baseUrl}reports',
        options: Options(
          headers: {
            'Authorization': 'Bearer $token',
            'Accept': 'application/json',
          },
        ),
      );

      if (response.statusCode == 200 && response.data != null) {
        final data = (response.data['data'] as List)
            .map((item) => ReportData.fromJson(item))
            .toList();

        if (data.isEmpty) {
          // ✅ إذا لم يتم العثور على تقارير
          emit(GetReportEmpty());
        } else {
          // ✅ إذا تم العثور على تقارير
          emit(GetReportSuccess(reportData: data, token: token));
        }
      } else {
        emit(GetReportError(errorMessage: "فشل في جلب البيانات"));
      }
    } on DioException catch (e) {
      log("حدث خطأ: ${e.response?.statusCode}");
      log("تفاصيل الخطأ: ${e.response?.data}");

      // ✅ إظهار رسالة الخطأ من API أو رسالة افتراضية
      emit(GetReportError(
        errorMessage: e.response?.data['message'] ?? "حدث خطأ غير متوقع",
      ));
    } catch (e) {
      // ✅ التقاط أي خطأ غير متوقع
      emit(GetReportError(errorMessage: "خطأ غير متوقع: $e"));
    }
  }
}
