import 'dart:developer';

import 'package:commitime_test/service/app_usage_service.dart';
import 'package:commitime_test/widget/apps_list_tile.dart';
import 'package:commitime_test/widget/streak.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:intl/intl.dart';
//import 'package:syncfusion_flutter_charts/charts.dart';
//import 'package:syncfusion_flutter_charts/sparkcharts.dart';

class Statictics extends StatefulWidget {
  const Statictics({super.key});

  @override
  State<Statictics> createState() => _StaticticsState();
}

class _StaticticsState extends State<Statictics> {
  // App usage state
  List<AppUsageData> _usageData = [];
  Map<String, int> _appLimits = {};
  bool _isLoading = true;
  int _selectedDays = 1; // Default to showing today's data
  bool _settingLimit = false; // Flag to track if we're setting a limit

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // Get usage stats and app limits concurrently
      final usageData = await AppUsageService.getUsageStats(_selectedDays);
      final appLimits = await AppUsageService.getAppLimits();

      setState(() {
        _usageData = usageData;
        _appLimits = appLimits;
        _isLoading = false;
      });
    } catch (e) {
      log("Error loading data: $e");
      setState(() {
        _isLoading = false;
      });
    }
  }

  // Format timestamp as time
  String _formatTimestamp(double timestamp) {
    final date = DateTime.fromMillisecondsSinceEpoch(timestamp.toInt());
    return DateFormat.jm().format(date); // Format as time (e.g., "3:30 PM")
  }

  // Calculate total screen time
  String get _totalScreenTime {
    double total = 0;
    for (var app in _usageData) {
      total += app.timeInForegroundMs;
    }

    final totalSeconds = total ~/ 1000;
    final hours = totalSeconds ~/ 3600;
    final minutes = (totalSeconds % 3600) ~/ 60;
    final seconds = totalSeconds % 60;

    return "${hours}h ${minutes}m ${seconds}s";
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        scrolledUnderElevation: 0,
        backgroundColor: Colors.white,
        centerTitle: true,
        title: const Text(
          "  Statistics",
          style: TextStyle(fontSize: 20, fontWeight: FontWeight.w500),
        ),
        actions: const [
          Streak(),
          SizedBox(
            width: 16,
          )
        ],
      ),
      body: _isLoading
          ? const Center(
              child: CircularProgressIndicator(color: Color(0xffBF65EB)))
          : RefreshIndicator(
              onRefresh: _loadData,
              color: const Color(0xffBF65EB),
              child: ListView(children: [
                Padding(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 22, vertical: 60),
                  child: SizedBox(
                    width: 350,
                    height: 220,
                    child: Stack(
                      children: [
                        Positioned(
                          child: Container(
                            decoration: const BoxDecoration(
                              color: Color(0xffF7E5FF),
                              borderRadius: BorderRadius.only(
                                  topLeft: Radius.circular(20)),
                            ),
                            height: 70,
                            width: 180,
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.center,
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                const Text(
                                  "Total time Usage",
                                  style: TextStyle(fontWeight: FontWeight.bold),
                                ),
                                Text(_totalScreenTime)
                              ],
                            ),
                          ),
                        ),
                        Positioned(
                          right: 2,
                          child: Container(
                            decoration: const BoxDecoration(
                              color: Color(0xffF7E5FF),
                              borderRadius: BorderRadius.only(
                                  topRight: Radius.circular(20)),
                            ),
                            height: 70,
                            width: 180,
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.center,
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                const Text("Total Apps Used",
                                    style:
                                        TextStyle(fontWeight: FontWeight.bold)),
                                Text("${_usageData.length}")
                              ],
                            ),
                          ),
                        ),
                        Positioned(
                          top: 75,
                          child: Container(
                            decoration: const BoxDecoration(
                              color: Color(0xffF7E5FF),
                            ),
                            height: 70,
                            width: 180,
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.center,
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                const Text("Highest Daily usage",
                                    style:
                                        TextStyle(fontWeight: FontWeight.bold)),
                                Text(_usageData.isNotEmpty
                                    ? " ${_usageData[0].formattedDuration}"
                                    : "None: 0h 0m")
                              ],
                            ),
                          ),
                        ),
                        Positioned(
                          top: 75,
                          right: 2,
                          child: Container(
                            decoration: const BoxDecoration(
                              color: Color(0xffF7E5FF),
                            ),
                            height: 70,
                            width: 180,
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.center,
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                const Text("Most Used App",
                                    style:
                                        TextStyle(fontWeight: FontWeight.bold)),
                                Text(_usageData.isNotEmpty
                                    ? _usageData[0].appName
                                    : "None")
                              ],
                            ),
                          ),
                        ),
                        Positioned(
                          top: 150,
                          child: Container(
                            decoration: const BoxDecoration(
                              color: Color(0xffF7E5FF),
                              borderRadius: BorderRadius.only(
                                  bottomLeft: Radius.circular(20)),
                            ),
                            height: 70,
                            width: 180,
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.center,
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                const Text("Limit Hits",
                                    style:
                                        TextStyle(fontWeight: FontWeight.bold)),
                                Text("${_appLimits.length}")
                              ],
                            ),
                          ),
                        ),
                        Positioned(
                          top: 150,
                          right: 2,
                          child: Container(
                            decoration: const BoxDecoration(
                              color: Color(0xffF7E5FF),
                              borderRadius: BorderRadius.only(
                                  bottomRight: Radius.circular(20)),
                            ),
                            height: 70,
                            width: 180,
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.center,
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                const Text("Apps Used",
                                    style:
                                        TextStyle(fontWeight: FontWeight.bold)),
                                Builder(
                                  builder: (context) {
                                    final significantAppsCount =
                                        _getSignificantApps().length;
                                    return Text("$significantAppsCount");
                                  },
                                )
                              ],
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),

                _buildTimePeriodSelector(),

                const SizedBox(height: 20),

                const Padding(
                  padding: EdgeInsets.symmetric(horizontal: 32),
                  child: Divider(
                    thickness: 2,
                    height: 7,
                    color: Colors.grey,
                  ),
                ),
                const SizedBox(
                  height: 8,
                ),
                const Center(
                  child: Text(
                    "App Usage",
                    style: TextStyle(fontSize: 20, fontWeight: FontWeight.w500),
                  ),
                ),

                const SizedBox(height: 16),

                // App usage list with time limits
                Builder(
                  builder: (context) {
                    // Get filtered apps using the helper method
                    final filteredApps = _getSignificantApps();

                    // Calculate total usage time for percentage calculations
                    double totalMs = 0;
                    for (var appData in _usageData) {
                      totalMs += appData.timeInForegroundMs;
                    }

                    if (filteredApps.isEmpty) {
                      return const Center(
                        child: Padding(
                          padding: EdgeInsets.all(32.0),
                          child: Text(
                            "No app usage data available for this period",
                            textAlign: TextAlign.center,
                            style: TextStyle(
                              fontSize: 16,
                              color: Colors.grey,
                            ),
                          ),
                        ),
                      );
                    } else {
                      return Column(
                        children: filteredApps.map((app) {
                          // Calculate percentage of total time
                          final rawPercentage = totalMs > 0
                              ? app.timeInForegroundMs / totalMs
                              : 0.0;
                          final formattedPercentageString = (rawPercentage *
                                  100)
                              .toStringAsFixed(1); // Format to 1 decimal place
                          final sliderValue =
                              rawPercentage.clamp(0.0, 1.0); // Clamp for slider
                          final currentLimit = _appLimits[app.packageName] ?? 0;
                          final String? limitText = currentLimit > 0
                              ? "Limit: $currentLimit min"
                              : null; // Calculate limit text

                          return GestureDetector(
                            child: AppsListTile(
                              pathImage: _getAppIconPath(app.packageName),
                              title: app.appName,
                              slidervalue:
                                  sliderValue, // Use clamped value for slider
                              time: app.formattedDuration,
                              percentageText:
                                  formattedPercentageString, // Pass formatted string
                              limitText: limitText, // Pass limit text
                              color: const Color(0xffE9ECF1),
                              iconBase64: app.iconBase64,
                            ),
                          );
                        }).toList(),
                      );
                    }
                  },
                ),
              ]),
            ),
    );
  }

  // Select time period (today, 3 days, 7 days)
  Widget _buildTimePeriodSelector() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          _buildPeriodButton(1, "Today"),
          const SizedBox(width: 10),
          _buildPeriodButton(3, "3 Days"),
          const SizedBox(width: 10),
          _buildPeriodButton(7, "Week"),
        ],
      ),
    );
  }

  Widget _buildPeriodButton(int days, String label) {
    final isSelected = _selectedDays == days;

    return GestureDetector(
      onTap: () {
        setState(() {
          _selectedDays = days;
        });
        _loadData();
      },
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        decoration: BoxDecoration(
          color: isSelected ? const Color(0xffBF65EB) : Colors.white,
          borderRadius: BorderRadius.circular(20),
          border: isSelected
              ? Border.all(color: const Color(0xffBF65EB))
              : Border.all(color: Colors.grey),
        ),
        child: Text(
          label,
          style: TextStyle(
            color: isSelected ? Colors.white : Colors.grey,
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
    );
  }

  // Get filtered apps that have been used with usage > 1%
  List<AppUsageData> _getSignificantApps() {
    // Calculate total usage time
    double totalMs = 0;
    for (var appData in _usageData) {
      totalMs += appData.timeInForegroundMs;
    }

    // Filter apps: only show apps used today with usage > 1%
    return _usageData.where((app) {
      // Calculate percentage of total time
      final percentage =
          totalMs > 0 ? (app.timeInForegroundMs / totalMs) * 100 : 0.0;
      // Only include apps with usage time > 0 and percentage > 1%
      return app.timeInForegroundMs > 0 && percentage > 1.0;
    }).toList();
  }

  // Helper method to get appropriate app icon path
  String _getAppIconPath(String packageName) {
    if (packageName.contains('facebook')) {
      return "assets/images/Facebook.png";
    } else if (packageName.contains('instagram')) {
      return "assets/images/Group 14.png";
    } else if (packageName.contains('youtube')) {
      return "assets/images/youtube.png";
    } else if (packageName.contains('whatsapp')) {
      return "assets/images/whatsapp.png";
    } else if (packageName.contains('music') ||
        packageName.contains('spotify')) {
      return "assets/images/Music.png";
    } else {
      // Default icon
      return "assets/images/Logo.png";
    }
  }
}
