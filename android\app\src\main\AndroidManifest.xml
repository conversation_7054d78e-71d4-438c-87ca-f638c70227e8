<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    package="com.example.commitime_test">

    <!-- Necessary to query app usage stats -->
    <uses-permission android:name="android.permission.PACKAGE_USAGE_STATS"
        tools:ignore="ProtectedPermissions" />

    <!-- Necessary for foreground service on Android 9+ -->
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />

    <!-- To restart service on boot -->
    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />

    <!-- Required to display overlay window for blocking -->
    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW"/>
    
    <!-- Permission to kill background processes (for app blocking) -->
    <uses-permission android:name="android.permission.KILL_BACKGROUND_PROCESSES" />
    
    <!-- Handle phone state changes (for more reliable blocking) -->
    <uses-permission android:name="android.permission.REORDER_TASKS" />
    
    <!-- Permission to get running tasks (needed for blocking) -->
    <uses-permission android:name="android.permission.GET_TASKS" />
    
    <!-- Basic internet permission -->
    <uses-permission android:name="android.permission.INTERNET" />

    <!-- Explicitly add the draw on top permission for older Android versions -->
    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
    
    <!-- Add the ability to restart immediately -->
    <uses-permission android:name="android.permission.REQUEST_IGNORE_BATTERY_OPTIMIZATIONS" />
    <uses-permission android:name="android.permission.WAKE_LOCK" />

    <!-- WorkManager permissions for background tasks -->
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.SCHEDULE_EXACT_ALARM" />
    <uses-permission android:name="android.permission.USE_EXACT_ALARM" />

    <application
        android:label="commitime_test"
        android:name="${applicationName}"
        android:icon="@mipmap/ic_launcher">
        <activity
            android:name=".MainActivity"
            android:exported="true"
            android:launchMode="singleTop"
            android:taskAffinity=""
            android:theme="@style/LaunchTheme"
            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
            android:hardwareAccelerated="true"
            android:windowSoftInputMode="adjustResize">
            <!-- Specifies an Android theme to apply to this Activity as soon as
                 the Android process has started. This theme is visible to the user
                 while the Flutter UI initializes. After that, this theme continues
                 to determine the Window background behind the Flutter UI. -->
            <meta-data
              android:name="io.flutter.embedding.android.NormalTheme"
              android:resource="@style/NormalTheme"
              />
            <intent-filter>
                <action android:name="android.intent.action.MAIN"/>
                <category android:name="android.intent.category.LAUNCHER"/>
            </intent-filter>
        </activity>

        <!-- App Usage Monitor Service -->
        <service
            android:name=".AppUsageMonitorService"
            android:exported="false"
            android:foregroundServiceType="dataSync" />

        <!-- Blocking Overlay Service -->
        <service
            android:name=".BlockingOverlayService"
            android:enabled="true"
            android:exported="false" />

        <!-- Blocking Activity -->
        <activity
            android:name=".BlockingActivity"
            android:theme="@style/BlockingActivityTheme"
            android:launchMode="singleInstance"
            android:taskAffinity=":blocking"
            android:excludeFromRecents="true"
            android:configChanges="orientation|screenSize"
            android:showOnLockScreen="true"
            android:showWhenLocked="true"
            android:turnScreenOn="true"
            android:exported="false"
            android:alwaysRetainTaskState="true"
            android:documentLaunchMode="never"
            android:lockTaskMode="normal">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </activity>

        <!-- Boot Receiver to restart service after device reboot -->
        <receiver 
            android:name=".BootReceiver"
            android:exported="true">
            <intent-filter>
                <action android:name="android.intent.action.BOOT_COMPLETED" />
            </intent-filter>
        </receiver>

        <!-- Don't delete the meta-data below.
             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java -->
        <meta-data
            android:name="flutterEmbedding"
            android:value="2" />
    </application>
    <!-- Required to query activities that existed on Android 10 (API level 29) and higher. -->
    <queries>
        <!-- Declare intent to query for launcher activities -->
        <intent>
            <action android:name="android.intent.action.MAIN" />
            <category android:name="android.intent.category.LAUNCHER" />
        </intent>
        
        <!-- If you need to query specific packages, declare them here -->
        <!-- Example: <package android:name="com.whatsapp" /> -->
    </queries>
</manifest>
