import 'dart:async';
import 'dart:convert';
import 'dart:developer' as developer;
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:workmanager/workmanager.dart';
import 'package:commitime_test/service/daily_report_service.dart';
import 'package:commitime_test/service/app_usage_service.dart';
import 'package:commitime_test/utils/shared_prefs.dart';

class AutomaticDailyReportService {
  static const String _dailyReportTaskName = "daily_report_task";
  static const String _backgroundTaskName = "background_daily_report";
  
  static Timer? _dailyTimer;
  static bool _isInitialized = false;

  /// Initialize the automatic daily report service
  static Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      // Initialize WorkManager for background tasks
      await Workmanager().initialize(
        callbackDispatcher,
        isInDebugMode: false, // Set to true for debugging
      );

      // Schedule the daily report
      await _scheduleDailyReport();
      
      // Set up in-app timer as backup
      _setupInAppTimer();
      
      _isInitialized = true;
      developer.log("Automatic daily report service initialized", name: "AutoDailyReport");
    } catch (e) {
      developer.log("Failed to initialize automatic daily report service: $e", 
          name: "AutoDailyReport", error: e);
    }
  }

  /// Schedule daily report using WorkManager (background execution)
  static Future<void> _scheduleDailyReport() async {
    try {
      // Cancel any existing tasks
      await Workmanager().cancelByUniqueName(_dailyReportTaskName);

      // Calculate time until next midnight (end of day)
      final now = DateTime.now();
      final tomorrow = DateTime(now.year, now.month, now.day + 1);
      final timeUntilMidnight = tomorrow.difference(now);

      developer.log("Scheduling daily report in ${timeUntilMidnight.inHours}h ${timeUntilMidnight.inMinutes % 60}m", 
          name: "AutoDailyReport");

      // Schedule the task to run at midnight
      await Workmanager().registerOneOffTask(
        _dailyReportTaskName,
        _backgroundTaskName,
        initialDelay: timeUntilMidnight,
        constraints: Constraints(
          networkType: NetworkType.connected,
          requiresBatteryNotLow: false,
          requiresCharging: false,
          requiresDeviceIdle: false,
          requiresStorageNotLow: false,
        ),
      );

      // Also schedule a periodic task as backup (runs every 24 hours)
      await Workmanager().registerPeriodicTask(
        "${_dailyReportTaskName}_periodic",
        _backgroundTaskName,
        frequency: const Duration(hours: 24),
        constraints: Constraints(
          networkType: NetworkType.connected,
          requiresBatteryNotLow: false,
          requiresCharging: false,
          requiresDeviceIdle: false,
          requiresStorageNotLow: false,
        ),
      );

    } catch (e) {
      developer.log("Failed to schedule daily report: $e", 
          name: "AutoDailyReport", error: e);
    }
  }

  /// Set up in-app timer as backup for when app is running
  static void _setupInAppTimer() {
    _cancelInAppTimer();

    final now = DateTime.now();
    final tomorrow = DateTime(now.year, now.month, now.day + 1);
    final timeUntilMidnight = tomorrow.difference(now);

    _dailyTimer = Timer(timeUntilMidnight, () async {
      await _sendDailyReportNow();
      // Reschedule for next day
      _setupInAppTimer();
    });

    developer.log("In-app timer set for ${timeUntilMidnight.inHours}h ${timeUntilMidnight.inMinutes % 60}m", 
        name: "AutoDailyReport");
  }

  /// Cancel in-app timer
  static void _cancelInAppTimer() {
    _dailyTimer?.cancel();
    _dailyTimer = null;
  }

  /// Send daily report immediately
  static Future<bool> _sendDailyReportNow() async {
    try {
      developer.log("Starting automatic daily report send", name: "AutoDailyReport");

      // Check if user is authenticated
      final token = await SharedPrefs.getToken();
      if (token == null || token.isEmpty) {
        developer.log("No authentication token found, skipping daily report", 
            name: "AutoDailyReport");
        return false;
      }

      // Get today's usage data
      final usageData = await AppUsageService.getUsageStats(1);
      if (usageData.isEmpty) {
        developer.log("No usage data found for today", name: "AutoDailyReport");
        return false;
      }

      // Convert usage data to the format expected by the API
      final Map<String, int> appUsageData = {};
      for (var app in usageData) {
        final usageMinutes = (app.timeInForegroundMs / (1000 * 60)).round();
        if (usageMinutes > 1) { // Only include apps with more than 1 minute usage
          appUsageData[app.appName] = usageMinutes;
        }
      }

      if (appUsageData.isEmpty) {
        developer.log("No apps with significant usage (>1 min) found", 
            name: "AutoDailyReport");
        return false;
      }

      // Send the report
      final dailyReportService = DailyReportService(dio: Dio());
      final result = await dailyReportService.sendDailyReport(appUsageData);

      developer.log("Daily report sent successfully: ${result.message}", 
          name: "AutoDailyReport");

      // Save last report date to prevent duplicate sends
      await SharedPrefs.setLastReportDate(DateTime.now().toIso8601String().split('T')[0]);

      return true;
    } catch (e) {
      developer.log("Failed to send automatic daily report: $e", 
          name: "AutoDailyReport", error: e);
      return false;
    }
  }

  /// Manual trigger for testing or immediate send
  static Future<bool> sendReportNow() async {
    return await _sendDailyReportNow();
  }

  /// Check if report was already sent today
  static Future<bool> wasReportSentToday() async {
    try {
      final lastReportDate = await SharedPrefs.getLastReportDate();
      if (lastReportDate == null) return false;

      final today = DateTime.now().toIso8601String().split('T')[0];
      return lastReportDate == today;
    } catch (e) {
      return false;
    }
  }

  /// Reschedule the daily report (call this when app starts)
  static Future<void> reschedule() async {
    if (!_isInitialized) {
      await initialize();
      return;
    }

    // Check if we need to send today's report
    final wasReportSent = await wasReportSentToday();
    if (!wasReportSent) {
      // Try to send today's report now
      await _sendDailyReportNow();
    }

    // Reschedule for tomorrow
    await _scheduleDailyReport();
    _setupInAppTimer();
  }

  /// Stop the automatic daily report service
  static Future<void> stop() async {
    _cancelInAppTimer();
    await Workmanager().cancelByUniqueName(_dailyReportTaskName);
    await Workmanager().cancelByUniqueName("${_dailyReportTaskName}_periodic");
    _isInitialized = false;
    developer.log("Automatic daily report service stopped", name: "AutoDailyReport");
  }
}

/// Background task callback dispatcher
@pragma('vm:entry-point')
void callbackDispatcher() {
  Workmanager().executeTask((task, inputData) async {
    try {
      developer.log("Background task started: $task", name: "AutoDailyReport");
      
      if (task == AutomaticDailyReportService._backgroundTaskName) {
        final success = await AutomaticDailyReportService._sendDailyReportNow();
        developer.log("Background daily report result: $success", name: "AutoDailyReport");
        return success;
      }
      
      return true;
    } catch (e) {
      developer.log("Background task failed: $e", name: "AutoDailyReport", error: e);
      return false;
    }
  });
}
