import 'dart:developer';
import 'package:shared_preferences/shared_preferences.dart';

class SharedPrefs {
  static const String _isLoggedInKey = 'isLoggedIn';
  static const String _userTokenKey = 'userToken';
  static const String _skipOnboardingKey = 'skipOnboarding';
  static const String _userNameKey = 'userName';
  static const String _userEmailKey = 'userEmail';
  static const String _chatAppNameKey = 'chat_app_name';
  static const String _chatUsageTimeKey = 'chat_usage_time';

  // Get a string value synchronously (for use in route generation)
  static String? getStringSync(String key) {
    try {
      final prefs = SharedPreferences.getInstance();
      // This is not ideal as it's not truly synchronous, but it's a workaround
      return ''; // Return empty string as fallback
    } catch (e) {
      return null;
    }
  }

  // Save chat app name
  static Future<bool> setChatAppName(String appName) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final result = await prefs.setString(_chatAppNameKey, appName);
      log('Saved chat app name: $appName, result: $result');
      return result;
    } catch (e) {
      log('Error saving chat app name: $e');
      return false;
    }
  }

  // Get chat app name
  static Future<String?> getChatAppName() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final appName = prefs.getString(_chatAppNameKey);
      log('Retrieved chat app name: $appName');
      return appName;
    } catch (e) {
      log('Error getting chat app name: $e');
      return null;
    }
  }

  // Save chat usage time
  static Future<bool> setChatUsageTime(String usageTime) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final result = await prefs.setString(_chatUsageTimeKey, usageTime);
      log('Saved chat usage time: $usageTime, result: $result');
      return result;
    } catch (e) {
      log('Error saving chat usage time: $e');
      return false;
    }
  }

  // Get chat usage time
  static Future<String?> getChatUsageTime() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final usageTime = prefs.getString(_chatUsageTimeKey);
      log('Retrieved chat usage time: $usageTime');
      return usageTime;
    } catch (e) {
      log('Error getting chat usage time: $e');
      return null;
    }
  }

  // Save chat parameters in one call
  static Future<bool> saveChatParameters(
      String appName, String usageTime) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final result1 = await prefs.setString(_chatAppNameKey, appName);
      final result2 = await prefs.setString(_chatUsageTimeKey, usageTime);
      log('Saved chat parameters - appName: $appName, usageTime: $usageTime, results: $result1, $result2');
      return result1 && result2;
    } catch (e) {
      log('Error saving chat parameters: $e');
      return false;
    }
  }

  // Save login status
  static Future<bool> setLoggedIn(bool value) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return await prefs.setBool(_isLoggedInKey, value);
    } catch (e) {
      log('Error setting login status: $e');
      return false;
    }
  }

  // Check if user is logged in
  static Future<bool> isLoggedIn() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getBool(_isLoggedInKey) ?? false;
    } catch (e) {
      log('Error checking login status: $e');
      return false;
    }
  }

  // Save user token
  static Future<bool> setUserToken(String token) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return await prefs.setString(_userTokenKey, token);
    } catch (e) {
      log('Error setting user token: $e');
      return false;
    }
  }

  // Get user token
  static Future<String?> getUserToken() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getString(_userTokenKey);
    } catch (e) {
      log('Error getting user token: $e');
      return null;
    }
  }

  // Set onboarding completed
  static Future<bool> setSkipOnboarding(bool value) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return await prefs.setBool(_skipOnboardingKey, value);
    } catch (e) {
      log('Error setting skip onboarding: $e');
      return false;
    }
  }

  // Check if onboarding should be skipped
  static Future<bool> shouldSkipOnboarding() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getBool(_skipOnboardingKey) ?? false;
    } catch (e) {
      log('Error checking skip onboarding: $e');
      return false;
    }
  }

  // Save user name
  static Future<bool> setUserName(String name) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return await prefs.setString(_userNameKey, name);
    } catch (e) {
      log('Error setting user name: $e');
      return false;
    }
  }

  // Get user name
  static Future<String?> getUserName() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getString(_userNameKey);
    } catch (e) {
      log('Error getting user name: $e');
      return null;
    }
  }

  // Save user email
  static Future<bool> setUserEmail(String email) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return await prefs.setString(_userEmailKey, email);
    } catch (e) {
      log('Error setting user email: $e');
      return false;
    }
  }

  // Get user email
  static Future<String?> getUserEmail() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getString(_userEmailKey);
    } catch (e) {
      log('Error getting user email: $e');
      return null;
    }
  }

  // Clear all preferences (for logout)
  static Future<bool> clearAll() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return await prefs.clear();
    } catch (e) {
      log('Error clearing preferences: $e');
      return false;
    }
  }
}
