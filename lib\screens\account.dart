import 'dart:developer';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:commitime_test/cubits/get_user_data/get_user_data_cubit.dart';
import 'package:commitime_test/models/user_cubit_model.dart';
import 'package:commitime_test/screens/logo.dart';
import 'package:commitime_test/utils/shared_prefs.dart';
import 'package:commitime_test/widget/account_list_tile.dart';
import 'package:commitime_test/widget/streak.dart';

class AccountPage extends StatefulWidget {
  const AccountPage({super.key});

  @override
  State<AccountPage> createState() => _AccountPageState();
}

class _AccountPageState extends State<AccountPage> {
  // Handle logout with proper async/await and context handling
  Future<void> _handleLogout(BuildContext context) async {
    // Show confirmation dialog
    bool confirm = await showDialog(
          context: context,
          builder: (context) => AlertDialog(
            title: const Text('Logout'),
            content: const Text('Are you sure you want to logout?'),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context, false),
                child: const Text('Cancel'),
              ),
              TextButton(
                onPressed: () => Navigator.pop(context, true),
                child: const Text('Logout'),
              ),
            ],
          ),
        ) ??
        false;

    if (confirm && mounted) {
      // Clear shared preferences
      bool cleared = await SharedPrefs.clearAll();

      if (!cleared) {
        log("Warning: Failed to clear shared preferences");
      }

      // Navigate to Logo screen and clear all routes
      if (!mounted) return;

      // Use a fresh context that's guaranteed to be valid
      Navigator.of(context).pushAndRemoveUntil(
        MaterialPageRoute(builder: (_) => const Logo()),
        (route) => false,
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        backgroundColor: Colors.white,
        appBar: AppBar(
          backgroundColor: Colors.white,
          centerTitle: true,
          title: Text(
            "  Profile",
            style: TextStyle(fontSize: 20, fontWeight: FontWeight.w500),
          ),
          actions: [Streak(), SizedBox(width: 16)],
        ),
        body: BlocBuilder<GetUserDataCubit, UserCubitModel?>(
          builder: (context, state) {
            return ListView(children: [
              Padding(
                padding:
                    const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Container(
                      height: 90,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(20),
                        gradient: LinearGradient(colors: [
                          Color(0xffD57DFF),
                          Color(0xff9421CB),
                          Color(0xffCB6BF8)
                        ]),
                      ),
                      child: Padding(
                        padding: const EdgeInsets.all(8),
                        child: ListTile(
                          leading: CircleAvatar(
                            radius: 32,
                            backgroundColor: Color(0xff9421CB),
                            child: Text(
                              state != null && state.name.isNotEmpty
                                  ? state.name[0].toUpperCase()
                                  : "U",
                              style: TextStyle(
                                  color: Colors.white,
                                  fontSize: 24,
                                  fontWeight: FontWeight.w700),
                            ),
                          ),
                          title: Text(
                            state != null && state.name.isNotEmpty
                                ? state.name[0].toUpperCase() +
                                    state.name.substring(1)
                                : "User",
                            style: TextStyle(
                                color: Colors.white,
                                fontSize: 20,
                                fontWeight: FontWeight.w700),
                          ),
                          subtitle: Text(
                            state != null && state.email.isNotEmpty
                                ? state.email
                                : "<EMAIL>",
                            style: TextStyle(
                                color: Color(0xffD7D7D7),
                                fontSize: 14,
                                fontWeight: FontWeight.w500),
                          ),
                          trailing: IconButton(
                              onPressed: () {},
                              icon: Icon(
                                Icons.edit_outlined,
                                color: Colors.white,
                                size: 32,
                              )),
                        ),
                      ),
                    ),
                    SizedBox(
                      height: 16,
                    ),
                    Container(
                        height: 350,
                        decoration: BoxDecoration(boxShadow: [
                          BoxShadow(
                              color: Colors.grey,
                              blurStyle: BlurStyle.outer,
                              spreadRadius: 1,
                              blurRadius: 4)
                        ]),
                        child: Column(
                          children: [
                            AccountListTile(
                              iconAvatar: Icons.person,
                              title: "Delete Account",
                              subtitle: "Make Changes To Your Account",
                              iconTrailing: Icons.warning_rounded,
                            ),
                            AccountListTile(
                              iconAvatar: Icons.person,
                              title: "Change Account",
                              subtitle: "Manage Your Saved Account",
                            ),
                            GestureDetector(
                              onTap: () {
                                _handleLogout(context);
                              },
                              child: AccountListTile(
                                iconAvatar: Icons.logout_outlined,
                                title: "Log Out",
                                subtitle:
                                    "Further Secure Your Account For Safety",
                              ),
                            ),
                          ],
                        )),
                    SizedBox(
                      height: 16,
                    ),
                    Text(
                      "More",
                      style: TextStyle(
                          color: Color(0xff181D27),
                          fontSize: 16,
                          fontWeight: FontWeight.w600),
                      textAlign: TextAlign.left,
                    ),
                    SizedBox(
                      height: 16,
                    ),
                    Container(
                      height: 200,
                      decoration: BoxDecoration(boxShadow: [
                        BoxShadow(
                            color: Colors.grey,
                            blurStyle: BlurStyle.outer,
                            spreadRadius: 1,
                            blurRadius: 4)
                      ]),
                      child: Column(
                        children: [
                          AccountListTile(
                            iconAvatar: Icons.help,
                            title: "Help & Support",
                            subtitle: "",
                          ),
                          AccountListTile(
                            iconAvatar: Icons.info_outline,
                            title: "About App",
                            subtitle: "",
                          ),
                        ],
                      ),
                    )
                  ],
                ),
              ),
            ]);
          },
        ));
  }
}
