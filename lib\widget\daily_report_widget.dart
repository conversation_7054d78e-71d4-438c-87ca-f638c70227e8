import 'package:flutter/material.dart';
import 'package:commitime_test/service/automatic_daily_report_service.dart';

class DailyReportWidget extends StatefulWidget {
  const DailyReportWidget({super.key});

  @override
  State<DailyReportWidget> createState() => _DailyReportWidgetState();
}

class _DailyReportWidgetState extends State<DailyReportWidget> {
  bool _isLoading = false;
  String? _lastResult;

  Future<void> _sendReportNow() async {
    setState(() {
      _isLoading = true;
      _lastResult = null;
    });

    try {
      final success = await AutomaticDailyReportService.sendReportNow();
      setState(() {
        _lastResult = success 
            ? "Daily report sent successfully!" 
            : "Failed to send daily report. Check logs for details.";
      });
    } catch (e) {
      setState(() {
        _lastResult = "Error: $e";
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _checkReportStatus() async {
    final wasReportSent = await AutomaticDailyReportService.wasReportSentToday();
    setState(() {
      _lastResult = wasReportSent 
          ? "Daily report was already sent today." 
          : "Daily report not sent today yet.";
    });
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.all(16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Daily Report',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Color(0xffBF65EB),
              ),
            ),
            const SizedBox(height: 8),
            const Text(
              'Automatic daily usage reports are sent at the end of each day.',
              style: TextStyle(fontSize: 14, color: Colors.grey),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton(
                    onPressed: _isLoading ? null : _sendReportNow,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: const Color(0xffBF65EB),
                      foregroundColor: Colors.white,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    child: _isLoading
                        ? const SizedBox(
                            height: 20,
                            width: 20,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                            ),
                          )
                        : const Text('Send Now'),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: OutlinedButton(
                    onPressed: _isLoading ? null : _checkReportStatus,
                    style: OutlinedButton.styleFrom(
                      foregroundColor: const Color(0xffBF65EB),
                      side: const BorderSide(color: Color(0xffBF65EB)),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    child: const Text('Check Status'),
                  ),
                ),
              ],
            ),
            if (_lastResult != null) ...[
              const SizedBox(height: 12),
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: _lastResult!.contains('successfully') 
                      ? Colors.green.withOpacity(0.1)
                      : _lastResult!.contains('Error') || _lastResult!.contains('Failed')
                          ? Colors.red.withOpacity(0.1)
                          : Colors.blue.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: _lastResult!.contains('successfully') 
                        ? Colors.green
                        : _lastResult!.contains('Error') || _lastResult!.contains('Failed')
                            ? Colors.red
                            : Colors.blue,
                    width: 1,
                  ),
                ),
                child: Text(
                  _lastResult!,
                  style: TextStyle(
                    fontSize: 12,
                    color: _lastResult!.contains('successfully') 
                        ? Colors.green.shade700
                        : _lastResult!.contains('Error') || _lastResult!.contains('Failed')
                            ? Colors.red.shade700
                            : Colors.blue.shade700,
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }
}
