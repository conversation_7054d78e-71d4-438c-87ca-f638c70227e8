import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:commitime_test/cubits/get_report_data/get_report_data_cubit.dart';
import 'package:commitime_test/cubits/get_report_data/get_report_state.dart';
import 'package:commitime_test/screens/no_report_data.dart';
import 'package:commitime_test/widget/report_list_tile.dart';
import 'package:commitime_test/widget/streak.dart';

class Report extends StatefulWidget {
  const Report({super.key, required this.token});
  final String? token;

  @override
  State<Report> createState() => _ReportState();
}

class _ReportState extends State<Report> {
  bool isSelected = false;

  void selected() {
    setState(() {
      isSelected = !isSelected;
    });
  }

  @override
  void initState() {
    super.initState();
    BlocProvider.of<GetReportDataCubit>(context).getReport(widget.token!);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        backgroundColor: Colors.white,
        centerTitle: true,
        title: Text(
          "  Report",
          style: TextStyle(fontSize: 20, fontWeight: FontWeight.w500),
        ),
        actions: const [Streak(), SizedBox(width: 16)],
      ),
      body: BlocListener<GetReportDataCubit, GetReportState>(
        listener: (context, state) {
          if (state is GetReportSuccess) {}
        },
        child: BlocBuilder<GetReportDataCubit, GetReportState>(
          builder: (context, state) {
            if (state is GetReportLoading) {
              return const Center(child: CircularProgressIndicator());
            } else if (state is GetReportEmpty) {
              return Center(child: const NoReportData());
            } else if (state is GetReportSuccess) {
              return ListView.builder(
                itemCount: state.reportData.length,
                itemBuilder: (context, index) {
                  return ReportTile(reportData: state.reportData[index]);
                },
              );
            } /*else if (state is GetReportError) {
            return Padding(
              padding: const EdgeInsets.all(16.0),
              child: Center(
                child: Text(state.errorMessage),
              ),
            );
          }*/
            return Center(child: const NoReportData());
          },
        ),
      ),
    );
  }
}
/*PageView(children: [
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(
                    decoration: BoxDecoration(
                      boxShadow: [
                        BoxShadow(
                          color: Colors.grey.shade600,
                          spreadRadius: .1,
                          offset: const Offset(0, 0),
                          blurStyle: BlurStyle.outer,
                          blurRadius: 10,
                        ),
                      ],
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: TextFormField(
                      decoration: InputDecoration(
                        enabled: false,
                        border: OutlineInputBorder(
                          borderSide: const BorderSide(color: Colors.white),
                          borderRadius: BorderRadius.circular(20),
                        ),
                        prefixIcon: const Icon(
                          Icons.search,
                          color: Color(0xffEC4CFF),
                        ),
                        hintText: "Search",
                        hintStyle: const TextStyle(color: Colors.grey),
                        suffixIcon: const Padding(
                          padding: EdgeInsets.all(10),
                          child: CircleAvatar(
                            radius: 20,
                            backgroundImage:
                                AssetImage("assets/images/User image.png"),
                          ),
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(height: 16),
                  const Text(
                    "INBOX",
                    style: TextStyle(color: Color(0xff686B70), fontSize: 16),
                  ),
                  const SizedBox(height: 16),
                  if (state is GetReportLoading)
                    const Center(child: CircularProgressIndicator()),
                  if (state is GetReportInitial) const NoReportData(),
                  if (state is GetReportSuccess)
                    Expanded(
                      child: ListView.builder(
                        itemCount: state.reportData.length,
                        itemBuilder: (context, index) {
                          return ReportTile(
                              reportData: state.reportData[index]);
                        },
                      ),
                    ),
                ],
              ),
            ),
          ]);*/
