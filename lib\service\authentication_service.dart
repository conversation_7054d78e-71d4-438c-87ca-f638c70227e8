import 'dart:developer';
import 'package:dio/dio.dart';
import 'package:commitime_test/models/api_model/authentication_model.dart';

class AuthenticationService {
  final Dio dio;
  final String baseUrl = 'https://8ec4-197-35-43-30.ngrok-free.app/api/';
  final String registerEndpoint = 'register';
  final String logINendpoint = 'login';

  AuthenticationService({required this.dio});

  Future registerAdult({
    required String firstName,
    required String lastName,
    required String email,
    bool isUnder18 = false,
    String parentEmail = "",
    required String password,
  }) async {
    try {
      Response response = await dio.post(
        "$baseUrl$registerEndpoint",
        data: {
          'first_name': firstName,
          'last_name': lastName,
          'email': email,
          'is_under_18': isUnder18,
          'password': password,
          if (isUnder18 == true) 'parent_email': parentEmail
        },
      );

      if (response.statusCode == 201) {
        log(response.data.toString());
        return response.data;
      } else {
        throw Exception("Failed to Register, ${response.statusCode}");
      }
    } on DioException catch (e) {
      final data = e.response?.data;
      String errMessage = "Oops, there was an error. Try again later.";

      if (data is Map<String, dynamic> && data.containsKey('message')) {
        errMessage = data['message'];
      } else if (data is String) {
        errMessage = data;
      }

      throw Exception(errMessage);
    }
  }

  Future logINAdult({required String email, required String password}) async {
    try {
      Response response = await dio.post("$baseUrl$logINendpoint", data: {
        'email': email,
        'password': password,
      });

      if (response.statusCode == 200) {
        log(response.data.toString());
        return AuthenticationModel.fromJson(response.data);
      } else {
        throw Exception("Failed to log in, ${response.statusCode}");
      }
    } on DioException catch (e) {
      final String errMessage = e.response?.data?['message'] ??
          "Oops, there was an error. Try again later.";
      throw Exception(errMessage);
    }
  }
}
